/**
 * 水面渲染器
 * 负责高质量水面的渲染，包括反射、折射、波纹等效果
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';
import type { Camera   } from '../Camera';
import { Scene } from '../../scene/Scene';
import { WaterBodyComponent, WaterBodyType } from '../../physics/water/WaterBodyComponent';
import { WaterMaterial } from './WaterMaterial';
import { Debug } from '../../utils/Debug';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 水面渲染器配置
 */
export interface WaterSurfaceRendererConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否启用反射 */
  enableReflection?: boolean;
  /** 是否启用折射 */
  enableRefraction?: boolean;
  /** 是否启用因果波纹 */
  enableCaustics?: boolean;
  /** 是否启用水下雾效 */
  enableUnderwaterFog?: boolean;
  /** 是否启用水下扭曲 */
  enableUnderwaterDistortion?: boolean;
  /** 是否启用深度测试 */
  enableDepthTest?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 反射贴图分辨率 */
  reflectionMapResolution?: number;
  /** 折射贴图分辨率 */
  refractionMapResolution?: number;
  /** 是否使用屏幕空间反射 */
  useScreenSpaceReflection?: boolean;
  /** 是否使用高质量波动 */
  useHighQualityWaves?: boolean;
  /** 是否使用GPU加速 */
  useGPUAcceleration?: boolean;
}

/**
 * 水面渲染器
 */
export class WaterSurfaceRenderer extends System {
  /** 系统类型 */
  public static readonly TYPE: string = 'WaterSurfaceRenderer';

  /** 配置 */
  private config: WaterSurfaceRendererConfig;
  /** 水体组件映射 */
  private waterBodies: Map<string, WaterBodyComponent> = new Map();
  /** 水体材质映射 */
  private waterMaterials: Map<string, WaterMaterial> = new Map();
  /** 反射相机 */
  private reflectionCamera: THREE.PerspectiveCamera | null = null;
  /** 折射相机 */
  private refractionCamera: THREE.PerspectiveCamera | null = null;
  /** 反射渲染目标 */
  private reflectionRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 折射渲染目标 */
  private refractionRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 深度渲染目标 */
  private depthRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 因果波纹渲染目标 */
  private causticsRenderTarget: THREE.WebGLRenderTarget | null = null;
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 活跃相机 */
  private activeCamera: Camera | null = null;
  /** 活跃场景 */
  private activeScene: Scene | null = null;
  /** 渲染器 */
  private renderer: THREE.WebGLRenderer | null = null;
  /** 性能监视器 */
  private performanceMonitor: PerformanceMonitor = PerformanceMonitor.getInstance();
  /** 反射平面 */
  private reflectionPlane: THREE.Plane = new THREE.Plane();
  /** 折射平面 */
  private refractionPlane: THREE.Plane = new THREE.Plane();
  /** 临时矩阵 */
  private tempMatrix: THREE.Matrix4 = new THREE.Matrix4();
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 是否自动更新 */
  private autoUpdate: boolean;
  /** 更新频率 */
  private updateFrequency: number;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: WaterSurfaceRendererConfig = {}) {
    super();

    // 设置世界引用
    this.world = world;

    // 设置默认配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 1,
      enableReflection: config.enableReflection !== undefined ? config.enableReflection : true,
      enableRefraction: config.enableRefraction !== undefined ? config.enableRefraction : true,
      enableCaustics: config.enableCaustics !== undefined ? config.enableCaustics : true,
      enableUnderwaterFog: config.enableUnderwaterFog !== undefined ? config.enableUnderwaterFog : true,
      enableUnderwaterDistortion: config.enableUnderwaterDistortion !== undefined ? config.enableUnderwaterDistortion : true,
      enableDepthTest: config.enableDepthTest !== undefined ? config.enableDepthTest : true,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring !== undefined ? config.enablePerformanceMonitoring : false,
      reflectionMapResolution: config.reflectionMapResolution || 512,
      refractionMapResolution: config.refractionMapResolution || 512,
      useScreenSpaceReflection: config.useScreenSpaceReflection !== undefined ? config.useScreenSpaceReflection : false,
      useHighQualityWaves: config.useHighQualityWaves !== undefined ? config.useHighQualityWaves : true,
      useGPUAcceleration: config.useGPUAcceleration !== undefined ? config.useGPUAcceleration : true
    };

    this.autoUpdate = this.config.autoUpdate!;
    this.updateFrequency = this.config.updateFrequency!;

    // 初始化渲染目标
    this.initializeRenderTargets();
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return WaterSurfaceRenderer.TYPE;
  }

  /**
   * 初始化渲染目标
   */
  private initializeRenderTargets(): void {
    // 创建反射渲染目标
    if (this.config.enableReflection) {
      this.reflectionRenderTarget = new THREE.WebGLRenderTarget(
        this.config.reflectionMapResolution!,
        this.config.reflectionMapResolution!,
        {
          minFilter: THREE.LinearFilter,
          magFilter: THREE.LinearFilter,
          format: THREE.RGBAFormat,
          stencilBuffer: false
        }
      );
    }

    // 创建折射渲染目标
    if (this.config.enableRefraction) {
      this.refractionRenderTarget = new THREE.WebGLRenderTarget(
        this.config.refractionMapResolution!,
        this.config.refractionMapResolution!,
        {
          minFilter: THREE.LinearFilter,
          magFilter: THREE.LinearFilter,
          format: THREE.RGBAFormat,
          stencilBuffer: false
        }
      );
    }

    // 创建深度渲染目标
    if (this.config.enableDepthTest) {
      this.depthRenderTarget = new THREE.WebGLRenderTarget(
        this.config.refractionMapResolution!,
        this.config.refractionMapResolution!,
        {
          minFilter: THREE.LinearFilter,
          magFilter: THREE.LinearFilter,
          format: THREE.RGBAFormat,
          stencilBuffer: false
        }
      );
    }

    // 创建因果波纹渲染目标
    if (this.config.enableCaustics) {
      this.causticsRenderTarget = new THREE.WebGLRenderTarget(
        this.config.refractionMapResolution! / 2,
        this.config.refractionMapResolution! / 2,
        {
          minFilter: THREE.LinearFilter,
          magFilter: THREE.LinearFilter,
          format: THREE.RGBAFormat,
          stencilBuffer: false
        }
      );
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 获取渲染器
    const renderSystem = this.world.getSystemByType('RenderSystem');
    if (renderSystem && (renderSystem as any).getRenderer) {
      this.renderer = (renderSystem as any).getRenderer();
    }

    if (!this.renderer) {
      Debug.error('WaterSurfaceRenderer', '无法获取渲染器');
      return;
    }

    // 创建反射相机和折射相机
    this.createCameras();

    Debug.log('WaterSurfaceRenderer', '水面渲染器初始化完成');
  }

  /**
   * 创建相机
   */
  private createCameras(): void {
    // 创建反射相机
    this.reflectionCamera = new THREE.PerspectiveCamera(50, 1, 0.1, 1000);

    // 创建折射相机
    this.refractionCamera = new THREE.PerspectiveCamera(50, 1, 0.1, 1000);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.config.enabled || !this.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.updateFrequency !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.beginMeasure('waterSurfaceRenderUpdate');
    }

    // 获取活跃相机和场景
    this.updateActiveCamera();
    this.updateActiveScene();

    // 如果没有活跃相机或场景，则跳过
    if (!this.activeCamera || !this.activeScene) {
      return;
    }

    // 更新所有水体
    this.updateWaterBodies(deltaTime);

    // 如果启用了性能监控，结束计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.endMeasure('waterSurfaceRenderUpdate');
    }
  }

  /**
   * 更新活跃相机
   */
  private updateActiveCamera(): void {
    // 获取相机组件
    const cameras = this.world.getEntitiesByComponent('Camera');
    if (cameras.length === 0) {
      this.activeCamera = null;
      return;
    }

    // 获取第一个相机
    const cameraEntity = cameras[0];
    this.activeCamera = cameraEntity.getComponent('Camera') as any as any as Camera;
  }

  /**
   * 更新活跃场景
   */
  private updateActiveScene(): void {
    // 获取场景组件
    const scenes = this.world.getEntitiesByComponent('Scene');
    if (scenes.length === 0) {
      this.activeScene = null;
      return;
    }

    // 获取第一个场景
    const sceneEntity = scenes[0];
    this.activeScene = sceneEntity.getComponent('Scene') as any as any as Scene;
  }

  /**
   * 更新所有水体
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWaterBodies(deltaTime: number): void {
    // 遍历所有水体
    for (const [entityId, waterBody] of this.waterBodies) {
      // 如果水体未启用，则跳过
      if (!waterBody.isEnabled()) {
        continue;
      }

      // 更新水体
      this.updateWaterBody(entityId, waterBody, deltaTime);
    }
  }

  /**
   * 更新水体
   * @param entityId 实体ID
   * @param waterBody 水体组件
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateWaterBody(entityId: string, waterBody: WaterBodyComponent, deltaTime: number): void {
    // 如果启用了反射，更新反射贴图
    if (this.config.enableReflection && this.reflectionRenderTarget && this.reflectionCamera && this.activeCamera && this.activeScene) {
      this.updateReflectionMap(waterBody);
    }

    // 如果启用了折射，更新折射贴图
    if (this.config.enableRefraction && this.refractionRenderTarget && this.refractionCamera && this.activeCamera && this.activeScene) {
      this.updateRefractionMap(waterBody);
    }

    // 如果启用了因果波纹，更新因果波纹贴图
    if (this.config.enableCaustics && this.causticsRenderTarget && this.activeCamera && this.activeScene) {
      this.updateCausticsMap(waterBody);
    }

    // 更新水体材质
    this.updateWaterMaterial(entityId, waterBody);
  }

  /**
   * 更新反射贴图
   * @param waterBody 水体组件
   */
  private updateReflectionMap(waterBody: WaterBodyComponent): void {
    if (!this.reflectionRenderTarget || !this.reflectionCamera || !this.activeCamera || !this.activeScene || !this.renderer) {
      return;
    }

    // 获取水体位置和法线
    const waterPosition = waterBody.getPosition();
    const waterNormal = new THREE.Vector3(0, 1, 0); // 假设水面法线向上

    // 设置反射平面
    this.reflectionPlane.setFromNormalAndCoplanarPoint(waterNormal, waterPosition);

    // 复制相机参数
    this.reflectionCamera.copy(this.activeCamera.getThreeCamera());

    // 应用反射矩阵
    const reflectionMatrix = new THREE.Matrix4();
    reflectionMatrix.set(
      1, 0, 0, 0,
      0, -1, 0, 0,
      0, 0, 1, 0,
      0, 0, 0, 1
    );

    // 应用反射矩阵
    this.reflectionCamera.applyMatrix4(reflectionMatrix);

    // 设置相机位置
    const cameraPosition = this.activeCamera.getThreeCamera().position.clone();
    const cameraTarget = this.activeCamera.getThreeCamera().getWorldDirection(new THREE.Vector3()).clone();

    // 反射相机位置
    cameraPosition.y = -cameraPosition.y + 2 * waterPosition.y;
    this.reflectionCamera.position.copy(cameraPosition);

    // 反射相机目标
    cameraTarget.y = -cameraTarget.y;
    this.reflectionCamera.lookAt(cameraTarget.add(cameraPosition));

    // 设置渲染目标
    this.renderer.setRenderTarget(this.reflectionRenderTarget);

    // 清除渲染目标
    this.renderer.clear();

    // 渲染场景
    this.renderer.render(this.activeScene.getThreeScene(), this.reflectionCamera);

    // 重置渲染目标
    this.renderer.setRenderTarget(null);
  }

  /**
   * 更新折射贴图
   * @param waterBody 水体组件
   */
  private updateRefractionMap(waterBody: WaterBodyComponent): void {
    if (!this.refractionRenderTarget || !this.refractionCamera || !this.activeCamera || !this.activeScene || !this.renderer) {
      return;
    }

    // 获取水体位置和法线
    const waterPosition = waterBody.getPosition();
    const waterNormal = new THREE.Vector3(0, 1, 0); // 假设水面法线向上

    // 设置折射平面
    this.refractionPlane.setFromNormalAndCoplanarPoint(waterNormal, waterPosition);

    // 复制相机参数
    this.refractionCamera.copy(this.activeCamera.getThreeCamera());

    // 设置渲染目标
    this.renderer.setRenderTarget(this.refractionRenderTarget);

    // 清除渲染目标
    this.renderer.clear();

    // 渲染场景
    this.renderer.render(this.activeScene.getThreeScene(), this.refractionCamera);

    // 重置渲染目标
    this.renderer.setRenderTarget(null);
  }

  /**
   * 更新因果波纹贴图
   * @param waterBody 水体组件
   */
  private updateCausticsMap(waterBody: WaterBodyComponent): void {
    if (!this.causticsRenderTarget || !this.activeCamera || !this.activeScene || !this.renderer) {
      return;
    }

    // 获取水体位置
    const waterPosition = waterBody.getPosition();

    // 创建因果波纹材质
    const causticsMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: performance.now() * 0.001 },
        waterHeight: { value: waterPosition.y },
        sunDirection: { value: new THREE.Vector3(0, 1, 0) },
        waterColor: { value: waterBody.getColor() },
        causticsIntensity: { value: 0.5 }
      },
      vertexShader: `
        varying vec2 vUv;

        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform float waterHeight;
        uniform vec3 sunDirection;
        uniform vec3 waterColor;
        uniform float causticsIntensity;

        varying vec2 vUv;

        // 扰动函数
        vec2 distort(vec2 uv, float factor) {
          return uv + factor * vec2(
            sin(uv.y * 10.0 + time),
            cos(uv.x * 10.0 + time)
          );
        }

        // 计算因果波纹
        vec3 calculateCaustics(vec2 uv, float time) {
          // 扰动UV
          vec2 distortedUv1 = distort(uv, 0.02);
          vec2 distortedUv2 = distort(uv, 0.03);

          // 计算因果波纹
          float caustic1 = pow(0.5 + 0.5 * sin(distortedUv1.x * 30.0 + time * 2.0) * sin(distortedUv1.y * 30.0 + time * 3.0), 5.0);
          float caustic2 = pow(0.5 + 0.5 * sin(distortedUv2.x * 20.0 - time * 1.5) * sin(distortedUv2.y * 20.0 - time * 2.5), 5.0);

          // 混合两个因果波纹
          float caustic = max(caustic1, caustic2);

          // 应用颜色
          vec3 causticsColor = vec3(1.0, 0.95, 0.8) * caustic * causticsIntensity;

          return causticsColor;
        }

        void main() {
          // 计算焦散
          vec3 caustics = calculateCaustics(vUv, time);

          // 输出焦散颜色
          gl_FragColor = vec4(caustics, 1.0);
        }
      `
    });

    // 创建平面几何体
    const geometry = new THREE.PlaneGeometry(1, 1);

    // 创建网格
    const mesh = new THREE.Mesh(geometry, causticsMaterial);

    // 创建场景
    const scene = new THREE.Scene();
    scene.add(mesh);

    // 创建相机
    const camera = new THREE.OrthographicCamera(-0.5, 0.5, 0.5, -0.5, 0.1, 10);
    camera.position.z = 1;

    // 设置渲染目标
    this.renderer.setRenderTarget(this.causticsRenderTarget);

    // 清除渲染目标
    this.renderer.clear();

    // 渲染场景
    this.renderer.render(scene, camera);

    // 重置渲染目标
    this.renderer.setRenderTarget(null);
  }

  /**
   * 更新水体材质
   * @param entityId 实体ID
   * @param waterBody 水体组件
   */
  private updateWaterMaterial(entityId: string, waterBody: WaterBodyComponent): void {
    // 获取水体材质
    let material = this.waterMaterials.get(entityId);

    // 如果材质不存在，创建新材质
    if (!material) {
      material = this.createWaterMaterial(waterBody);
      this.waterMaterials.set(entityId, material);
    }

    // 更新材质贴图
    if (this.reflectionRenderTarget) {
      material.setReflectionMap(this.reflectionRenderTarget.texture);
    }

    if (this.refractionRenderTarget) {
      material.setRefractionMap(this.refractionRenderTarget.texture);
    }

    if (this.causticsRenderTarget) {
      material.setCausticsMap(this.causticsRenderTarget.texture);
    }

    // 更新材质时间
    material.updateTime(performance.now() * 0.001);
  }

  /**
   * 创建水体材质
   * @param waterBody 水体组件
   * @returns 水体材质
   */
  private createWaterMaterial(waterBody: WaterBodyComponent): WaterMaterial {
    // 创建水体材质
    const material = new WaterMaterial({
      color: waterBody.getColor(),
      opacity: waterBody.getOpacity(),
      reflectivity: waterBody.getReflectivity(),
      refractionRatio: waterBody.getRefractivity(),
      waveStrength: waterBody.getWaveParams().amplitude,
      waveSpeed: waterBody.getWaveParams().speed,
      waveScale: waterBody.getWaveParams().frequency,
      waveDirection: new THREE.Vector2(
        waterBody.getWaveParams().direction.x,
        waterBody.getWaveParams().direction.z
      ),
      enableReflection: this.config.enableReflection,
      enableRefraction: this.config.enableRefraction,
      enableCaustics: this.config.enableCaustics,
      enableFoam: true,
      enableUnderwaterFog: this.config.enableUnderwaterFog,
      enableUnderwaterDistortion: this.config.enableUnderwaterDistortion
    });

    return material;
  }

  /**
   * 添加水体组件
   * @param entity 实体
   * @param component 水体组件
   */
  public addWaterBody(entity: Entity, component: WaterBodyComponent): void {
    this.waterBodies.set(entity.id, component);

    // 创建水体材质
    const material = this.createWaterMaterial(component);
    this.waterMaterials.set(entity.id, material);

    Debug.log('WaterSurfaceRenderer', `添加水体组件: ${entity.id}`);
  }

  /**
   * 移除水体组件
   * @param entity 实体
   */
  public removeWaterBody(entity: Entity): void {
    this.waterBodies.delete(entity.id);
    this.waterMaterials.delete(entity.id);

    Debug.log('WaterSurfaceRenderer', `移除水体组件: ${entity.id}`);
  }
}
